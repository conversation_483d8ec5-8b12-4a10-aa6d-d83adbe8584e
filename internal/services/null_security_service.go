// Package services 提供各种服务实现
package services

import (
	"crypto/tls"
	"net/http"
	
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// nullSecurityService 空安全服务实现，用于禁用安全功能时
type nullSecurityService struct {
	logger logger.Logger
}

// NewNullSecurityService 创建空安全服务实例
func NewNullSecurityService(log logger.Logger) interfaces.SecurityService {
	if log == nil {
		log = logger.GetLogger("null-security")
	}
	
	return &nullSecurityService{
		logger: log,
	}
}

// ValidateToken 验证令牌（空实现，总是返回true）
func (nss *nullSecurityService) ValidateToken(token string) (bool, error) {
	return true, nil
}

// GenerateToken 生成令牌（空实现，返回空字符串）
func (nss *nullSecurityService) GenerateToken(userID string, scopes []string) (string, error) {
	return "", nil
}

// RefreshToken 刷新令牌（空实现，返回空字符串）
func (nss *nullSecurityService) RefreshToken(token string) (string, error) {
	return "", nil
}

// RevokeToken 撤销令牌（空实现）
func (nss *nullSecurityService) RevokeToken(token string) error {
	return nil
}

// EncryptData 加密数据（空实现，返回原始数据）
func (nss *nullSecurityService) EncryptData(data []byte) ([]byte, error) {
	return data, nil
}

// DecryptData 解密数据（空实现，返回原始数据）
func (nss *nullSecurityService) DecryptData(encryptedData []byte) ([]byte, error) {
	return encryptedData, nil
}

// GetTLSConfig 获取TLS配置（空实现，返回nil）
func (nss *nullSecurityService) GetTLSConfig() *tls.Config {
	return nil
}

// ValidateRequest 验证请求（空实现，总是返回true）
func (nss *nullSecurityService) ValidateRequest(req *http.Request) (bool, error) {
	return true, nil
}

// AuthenticateUser 用户认证（空实现，总是返回true）
func (nss *nullSecurityService) AuthenticateUser(username, password string) (bool, error) {
	return true, nil
}

// AuthorizeUser 用户授权（空实现，总是返回true）
func (nss *nullSecurityService) AuthorizeUser(userID string, resource string, action string) (bool, error) {
	return true, nil
}

// CleanupExpiredTokens 清理过期令牌（空实现，返回0）
func (nss *nullSecurityService) CleanupExpiredTokens() int {
	return 0
}

// Start 启动安全服务（空实现）
func (nss *nullSecurityService) Start() error {
	nss.logger.Info("空安全服务已启动（无实际功能）")
	return nil
}

// Stop 停止安全服务（空实现）
func (nss *nullSecurityService) Stop() error {
	nss.logger.Info("空安全服务已停止")
	return nil
}

// IsEnabled 检查服务是否启用（空实现，总是返回false）
func (nss *nullSecurityService) IsEnabled() bool {
	return false
}
