// Package services 提供各种服务实现
package services

import (
	"time"
	
	"github.com/mbndr/logo"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// nullProfilingService 空性能分析服务实现，用于禁用性能分析功能时
type nullProfilingService struct {
	logger *logo.Logger
}

// NewNullProfilingService 创建空性能分析服务实例
func NewNullProfilingService(log *logo.Logger) interfaces.ProfilingService {
	if log == nil {
		log = logo.NewSimpleLogger(nil, logo.INFO, "null-profiling", false)
	}
	
	return &nullProfilingService{
		logger: log,
	}
}

// Start 启动性能分析服务（空实现）
func (nps *nullProfilingService) Start() error {
	nps.logger.Info("空性能分析服务已启动（无实际功能）")
	return nil
}

// Stop 停止性能分析服务（空实现）
func (nps *nullProfilingService) Stop() error {
	nps.logger.Info("空性能分析服务已停止")
	return nil
}

// IsEnabled 检查服务是否启用（空实现，总是返回false）
func (nps *nullProfilingService) IsEnabled() bool {
	return false
}

// StartProfile 开始性能分析（空实现，返回空会话ID）
func (nps *nullProfilingService) StartProfile(profileType string, duration time.Duration) (string, error) {
	return "", nil
}

// StopProfile 停止性能分析（空实现）
func (nps *nullProfilingService) StopProfile(sessionID string) error {
	return nil
}

// GetProfile 获取性能分析结果（空实现，返回空数据）
func (nps *nullProfilingService) GetProfile(sessionID string) ([]byte, error) {
	return []byte{}, nil
}

// ListProfiles 列出性能分析会话（空实现，返回空列表）
func (nps *nullProfilingService) ListProfiles() []interface{} {
	return []interface{}{}
}

// DeleteProfile 删除性能分析结果（空实现）
func (nps *nullProfilingService) DeleteProfile(sessionID string) error {
	return nil
}

// GetStats 获取统计信息（空实现，返回空统计）
func (nps *nullProfilingService) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"enabled":        false,
		"active_sessions": 0,
		"total_profiles":  0,
		"total_size_bytes": 0,
		"cpu_profile":     false,
		"memory_profile":  false,
		"block_profile":   false,
		"mutex_profile":   false,
	}
}

// TakeSnapshot 拍摄快照（空实现，返回空数据）
func (nps *nullProfilingService) TakeSnapshot(profileType string) ([]byte, error) {
	return []byte{}, nil
}

// GetProfileInfo 获取性能分析信息（空实现，返回nil）
func (nps *nullProfilingService) GetProfileInfo(sessionID string) interface{} {
	return nil
}

// SetConfig 设置配置（空实现）
func (nps *nullProfilingService) SetConfig(config interface{}) error {
	return nil
}

// GetConfig 获取配置（空实现，返回nil）
func (nps *nullProfilingService) GetConfig() interface{} {
	return nil
}

// EnableProfile 启用特定类型的性能分析（空实现）
func (nps *nullProfilingService) EnableProfile(profileType string) error {
	return nil
}

// DisableProfile 禁用特定类型的性能分析（空实现）
func (nps *nullProfilingService) DisableProfile(profileType string) error {
	return nil
}

// IsProfileEnabled 检查特定类型的性能分析是否启用（空实现，总是返回false）
func (nps *nullProfilingService) IsProfileEnabled(profileType string) bool {
	return false
}

// CleanupOldProfiles 清理旧的性能分析文件（空实现，返回0）
func (nps *nullProfilingService) CleanupOldProfiles(maxAge time.Duration) (int, error) {
	return 0, nil
}

// GetMemoryUsage 获取内存使用情况（空实现，返回空信息）
func (nps *nullProfilingService) GetMemoryUsage() map[string]interface{} {
	return map[string]interface{}{
		"enabled": false,
		"alloc":   0,
		"sys":     0,
		"gc":      0,
	}
}

// GetCPUUsage 获取CPU使用情况（空实现，返回空信息）
func (nps *nullProfilingService) GetCPUUsage() map[string]interface{} {
	return map[string]interface{}{
		"enabled": false,
		"usage":   0.0,
	}
}
