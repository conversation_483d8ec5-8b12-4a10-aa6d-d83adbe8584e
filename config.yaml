# FlexProxy 配置文件
# 这是一个全面的代理应用配置模板，包含详细的中文注释说明

# ============================================================================
# 全局配置 (Global Configuration)
# ============================================================================
global:
  # 是否启用代理服务
  # 值: true/false
  # 说明: 控制整个代理服务的开关状态
  enable: true

  # 代理文件路径
  # 值: 文件路径字符串
  # 说明: 指定包含代理服务器列表的文件路径，支持相对路径和绝对路径
  # 示例: "./proxies.txt", "/etc/flexproxy/proxies.txt"
  proxy_file: "./proxies.txt"

  # 全局封禁IP配置列表
  # 说明: 配置需要全局封禁的IP地址及其封禁时长
  global_banned_ips:
    # IP地址: 需要封禁的IP地址
    # duration: 封禁时长，支持三种格式:
    #   1. 整数(秒数): 如 3600 表示3600秒
    #   2. Go时间格式字符串: 如 "1h30m", "300ms", "2s"
    #   3. 特殊值 "reboot": 重启后解封(永久封禁)
    - ip: "*************"
      duration: 3600  # 封禁1小时 (3600秒)
    - ip: "*********"
      duration: "1h30m"  # 封禁1小时30分钟 (Go duration格式)
    - ip: "***********"
      duration: "reboot"  # 重启后解封(永久封禁)

  # 全局封禁域名配置列表
  # 说明: 配置需要全局封禁的域名及其封禁时长
  banned_domains:
    # domain: 需要封禁的域名 (必须是有效的FQDN格式)
    # duration: 封禁时长，支持三种格式:
    #   1. 整数(秒数): 如 86400 表示86400秒
    #   2. Go时间格式字符串: 如 "24h", "30m", "2h15m"
    #   3. 特殊值 "reboot": 重启后解封(永久封禁)
    - domain: "malicious-site.com"
      duration: 86400  # 封禁24小时 (86400秒)
    - domain: "spam-domain.net"
      duration: "24h"  # 封禁24小时 (Go duration格式)
    - domain: "blocked-forever.com"
      duration: "reboot"  # 重启后解封(永久封禁)

  # 阻止访问的IP地址列表
  # 值: IP地址字符串数组
  # 说明: 直接阻止这些IP地址的所有请求，必须是有效的IP地址格式
  blocked_ips:
    - "***********"
    - "************"
    - "*********"

  # 信任的IP地址列表
  # 值: IP地址字符串数组
  # 说明: 这些IP地址的请求将被优先处理或跳过某些检查
  # 注意: 必须是单个IP地址，不支持CIDR表示法（如***********/24）
  # 如需支持IP段，请列出具体的IP地址
  trusted_ips:
    - "127.0.0.1"      # 本地回环地址IPv4
    - "::1"            # 本地回环地址IPv6
    - "***********"    # 示例：内网网关IP
    - "********"       # 示例：内网IP

  # 排除的URL模式列表
  # 值: 字符串数组
  # 说明: 匹配这些模式的URL将不会通过代理处理
  # 支持正则表达式和通配符模式
  excluded_patterns:
    - "*.local"
    - "localhost:*"
    - "127.0.0.1:*"
    - "*.internal.company.com"

  # 排除范围
  # 值: 字符串
  # 说明: 定义排除规则的应用范围和匹配方式
  # 可选值:
  #   - "extension": 仅匹配文件扩展名
  #   - "domain": 仅匹配域名
  #   - "url": 仅匹配完整URL
  #   - "all": 尝试所有匹配方式（URL、域名、扩展名）
  #   - 其他值: 默认行为，等同于"all"
  # 推荐值: "all" (最全面的匹配方式)
  excluded_scope: "all"

  # 规则优先级
  # 值: 0-100的整数
  # 说明: 定义规则处理的优先级，数值越高优先级越高
  # 0=最低优先级, 100=最高优先级
  rule_priority: 50

  # 默认处理阶段
  # 值: "pre", "post_header", "post_body"
  # 说明: 定义默认的请求处理阶段
  # - pre: 请求前处理
  # - post_header: 响应头处理
  # - post_body: 响应体处理
  default_process_stage: "pre"

  # DNS查找模式
  # 值: "system", "custom", "hybrid"
  # 说明: 定义DNS解析的方式
  # 注意: 代码中实际使用的常量是 "local", "remote", "custom"
  # 但验证标签要求 "system", "custom", "hybrid"，这里使用验证标签支持的值
  # - system: 使用系统DNS (对应代码中的"local")
  # - custom: 使用自定义DNS服务器
  # - hybrid: 混合模式，优先使用自定义DNS，失败时回退到系统DNS
  dns_lookup_mode: "custom"

  # 反向DNS查找
  # 值: "no", "dns", "file:/path/to/hosts.txt", 或直接映射值
  # 说明: 配置反向DNS查找的行为
  # - "no": 不进行反向DNS解析
  # - "dns": 使用系统DNS进行反向解析
  # - "file:/path/to/hosts.txt": 从文件加载IP到域名的映射 (格式: ******* example.com)
  # - 直接映射: "******* example.com, ******* another.com" (逗号分隔多个映射)
  reverse_dns_lookup: "dns"

  # 自定义DNS服务器配置列表
  # 说明: 配置自定义DNS服务器的详细参数
  # ✅ 更新说明:
  # - server字段现在支持两种格式：纯IP地址（如"*******"）或IP:端口格式（如"*******:53"）
  # - 验证器已更新为：validate:"required,ip_or_ip_port"
  # - 如果不指定端口，将根据protocol字段使用默认端口
  # - HTTPProxyDNS字段同样支持 "IP:端口" 格式
  custom_dns_servers:
    # 主要DNS服务器 - Cloudflare（纯IP地址格式）
    - server: "*******"  # 纯IP地址，使用协议默认端口
      protocol: "udp"    # 协议: udp, tcp, doh, dot (注意: 验证标签要求 "udp tcp doh dot")
      timeout: 5000      # 超时时间(毫秒): 1000-30000
      priority: 1        # 优先级: 0-100, 数值越小优先级越高
      tags: ["cloudflare", "primary"]


  # HTTP代理DNS设置
  # 值: DNS服务器地址字符串
  # 说明: 指定HTTP代理使用的DNS服务器，支持DoH (DNS over HTTPS)
  #
  # ✅ 支持的格式:
  # 1. 基本格式: "IP地址" (如 "*******") - 使用默认端口53
  # 2. 端口格式: "IP地址:端口" (如 "*******:5353") - 自定义端口
  # 3. DoH格式: "https://域名/path" (如 "https://*******/dns-query")
  # 4. 复杂格式: "IP:端口@协议,参数" (如 "*******:53@udp,timeout=5000")
  #
  # 示例: "*******:53", "*******:5353", "https://cloudflare-dns.com/dns-query"
  # 留空则使用系统默认DNS
  http_proxy_dns: "*******:53"

  # IP轮换模式
  # 值: "random", "sequential", "quality", "smart"
  # 说明: 定义代理IP的选择策略
  # - random: 随机选择
  # - sequential: 顺序选择
  # - quality: 基于质量评分选择
  # - smart: 智能选择（根据触发动作自动决定是否切换代理）
  ip_rotation_mode: "smart"

  # 最小代理池大小
  # 值: 大于等于1的整数
  # 说明: 维护的最小可用代理数量，低于此数量时会自动补充
  min_proxy_pool_size: 10       

  # 最大代理获取尝试次数
  # 值: 1-10的整数
  # 说明: 获取新代理时的最大尝试次数
  max_proxy_fetch_attempts: 3

  # DNS缓存TTL (生存时间)
  # 值: 大于等于0的整数 (秒)
  # 说明: DNS查询结果的缓存时间，0表示不缓存
  dns_cache_ttl: 300

  # 禁用DNS缓存
  # 值: true/false
  # 说明: 是否完全禁用DNS缓存功能
  dns_no_cache: false

  # IP版本优先级
  # 值: "ipv4", "ipv6", "dual"
  # 说明: 定义IP协议版本的使用优先级
  # - ipv4: 优先使用IPv4
  # - ipv6: 优先使用IPv6
  # - dual: 同时支持IPv4和IPv6
  ip_version_priority: "ipv4"

  # 默认DNS超时时间
  # 值: 1000-30000的整数 (毫秒)
  # 说明: DNS查询的默认超时时间
  default_dns_timeout: 5000

  # 重试代理复用策略
  # 值: "allow", "deny", "cooldown"
  # 说明: 定义失败后重试时是否可以复用相同的代理
  # - allow: 允许立即复用
  # - deny: 禁止复用
  # - cooldown: 冷却时间后可复用
  retry_proxy_reuse_policy: "cooldown"

  # 重试代理冷却时间
  # 值: 大于等于0的整数 (秒)
  # 说明: 代理失败后的冷却时间，在此期间不会被重新使用
  retry_proxy_cooldown_time: 60

  # 重试代理全局跟踪
  # 值: true/false
  # 说明: 是否在全局范围内跟踪代理的重试状态
  # true: 全局跟踪，所有请求共享代理状态
  # false: 独立跟踪，每个请求独立管理代理状态
  retry_proxy_global_tracking: true

# ================================
# 服务器配置
# ================================
# 定义HTTP服务器的基本配置
server:
  host: "0.0.0.0"               # 服务器监听地址
  port: 8080                    # 服务器监听端口: 1-65535
  https_port: 8443              # HTTPS端口: 1-65535
  socks_port: 1080              # SOCKS端口: 1-65535
  read_timeout: "30s"           # 读取超时时间
  write_timeout: "30s"          # 写入超时时间
  idle_timeout: "120s"          # 空闲超时时间
  connect_timeout: "10s"        # 连接超时时间
  max_idle_conns: 100           # 最大空闲连接数: >=0
  max_idle_conns_per_host: 10   # 每个主机最大空闲连接数: >=0
  max_conns_per_host: 50        # 每个主机最大连接数: >=0
  buffer_size: 4096             # 缓冲区大小: >=1024
  max_header_bytes: 1048576     # 最大请求头字节数: >=1024
  debounce_delay: "100ms"       # 防抖延迟

# ================================
# 代理配置
# ================================
# 定义代理服务器的配置
proxy:
  strategy: "random"           # 代理策略: random, sequential, quality, custom
  load_balancer: "round_robin" # 负载均衡器: round_robin, weighted_round_robin, least_connections, response_time, ip_hash
  max_retries: 3               # 最大重试次数: >=0
  retry_interval: "1s"         # 重试间隔
  max_retry_interval: "30s"    # 最大重试间隔
  pool_size: 100               # 代理池大小: >=1
  rotation_interval: 300       # 轮换间隔(秒): >=0

  # 健康检查配置
  health_check:
    enabled: true              # 是否启用健康检查
    interval: "30s"            # 检查间隔
    timeout: "10s"             # 检查超时时间
    path: "/health"            # 健康检查路径
    max_consecutive_failures: 3    # 最大连续失败次数: >=1
    max_consecutive_successes: 2   # 最大连续成功次数: >=1

  # 质量评分配置
  quality_score:
    default: 0.5                      # 默认质量分数: 0-1
    success_rate_weight: 0.6          # 成功率权重: 0-1
    response_time_weight: 0.4         # 响应时间权重: 0-1
    max_failure_rate: 0.3             # 最大失败率: 0-1
    top_proxy_ratio: 0.2              # 顶级代理比例: 0-1
    response_time_baseline: 1000      # 响应时间基线(毫秒): >=0
    smoothing_factor: 0.1             # 平滑因子: 0-1

# ================================
# 缓存配置
# ================================
# 定义缓存系统的配置
cache:
  type: "memory"               # 缓存类型: memory, redis, file
  ttl: "3600s"                 # 默认缓存TTL
  size: 1000                   # 最大缓存条目数: >=1
  cleanup_interval: "300s"     # 清理间隔

  # 缓存键前缀配置
  key_prefixes:
    proxy_list: "proxy:list"
    proxy_status: "proxy:status:"
    user_session: "user:session:"
    rate_limit: "rate:limit:"

  # DNS缓存配置
  dns:
    ttl: "300s"                # DNS缓存TTL
    cleanup_interval: "600s"   # DNS缓存清理间隔

# ================================
# 日志配置
# ================================
# 定义日志系统的配置
logging:
  level: "info"                # 日志级别: debug, info, warn, error, fatal
  format: "json"               # 日志格式: json, text
  file: "./logs/flexproxy.log" # 日志文件路径
  max_size: 100                # 日志文件最大大小(MB): >=1
  max_backups: 10              # 最大备份文件数: >=0
  max_age: 30                  # 日志文件最大保存天数: >=1
  time_format: "2006-01-02T15:04:05.000Z07:00" # 时间格式

# ================================
# 监控配置
# ================================
# 定义监控和指标收集的配置
monitoring:
  enabled: true                # 是否启用监控
  port: 9090                   # 监控端口: 1-65535
  path: "/metrics"             # 指标路径

  # 指标配置
  metrics:
    request_duration: "histogram"    # 请求持续时间指标类型
    request_count: "counter"         # 请求计数指标类型
    proxy_status: "gauge"            # 代理状态指标类型
    cache_hit_rate: "gauge"          # 缓存命中率指标类型

  # 标签配置
  labels:
    service: "flexproxy"       # 服务标签
    version: "1.0.0"           # 版本标签
    environment: "production"  # 环境标签

# ================================
# 安全配置
# ================================
# 定义安全相关的配置
security:
  # 认证配置
  auth:
    type: "none"               # 认证类型: none, basic, bearer, apikey
    token_expiry: "24h"        # Token过期时间

  # 加密配置
  encryption:
    algorithm: "aes256"        # 加密算法: aes256, rsa
    key_length: 32             # 密钥长度: >=16

  # TLS配置
  tls:
    enabled: false             # 是否启用TLS
    cert_file: ""              # 证书文件路径
    key_file: ""               # 私钥文件路径
    min_version: "1.2"         # 最小TLS版本
    max_version: "1.3"         # 最大TLS版本

# ================================
# 限流配置
# ================================
# 定义请求限流的配置
rate_limiting:
  enabled: false               # 是否启用限流
  algorithm: "token_bucket"    # 限流算法: token_bucket, leaky_bucket, fixed_window, sliding_window
  rate: 100                    # 限流速率(请求/秒): >=1
  burst: 200                   # 突发容量: >=1
  window: "1m"                 # 时间窗口
  cleanup_period: "5m"         # 清理周期

# ================================
# DNS服务配置
# ================================
# 定义DNS解析服务的配置
dns_service:
  enabled: true                # 是否启用DNS服务
  cache_ttl: "300s"           # DNS缓存TTL
  timeout: "5s"               # DNS查询超时时间
  retries: 3                  # DNS查询重试次数: >=0

  # DNS服务器配置
  servers:
    primary: "*******:53"     # 主DNS服务器
    secondary: "*******:53"   # 备用DNS服务器
    fallback: "system"        # 回退DNS: system, none

  # DNS模式配置
  modes:
    ipv4: "enabled"           # IPv4解析: enabled, disabled, preferred
    ipv6: "enabled"           # IPv6解析: enabled, disabled, preferred
    reverse: "enabled"        # 反向解析: enabled, disabled

# ================================
# 动作序列配置
# ================================
# 定义可重用的动作序列，供事件触发时执行
#
# ⚠️  重要架构说明：
# 当前FlexProxy存在两套动作系统，部分动作类型可能无法正常工作：
#
# ✅ 完全支持的动作类型（有Executor实现）：
#    log, banip, ban_domain, block_request, modify_request,
#    modify_response, cache_response, script
#
# ⚠️  部分支持的动作类型（仅有Action接口实现）：
#    retry_same, retry, banipdomain, save_to_pool, cache,
#    request_url, null_response, bypass_proxy
#
# ❌ 验证器不支持的动作类型（会被config_validator.go拒绝）：
#    alert, block, redirect, rate_limit, webhook, email, slack, discord, custom
#
# 建议：优先使用"完全支持"的动作类型以确保功能正常
actions:
  # 状态码处理动作序列
  status_handler:
    sequence:
      - type: "banip"
        duration: 300  # 封禁5分钟
      - type: "retry"
        retry_count: 2

  # 超时处理动作序列
  timeout_handler:
    sequence:
      - type: "retry_same"
        retry_count: 1
      - type: "save_to_pool"
        pool_name: "timeout_ips"

  # 缓存动作序列
  cache_handler:
    sequence:
      - type: "cache"
        duration: 3600  # 缓存1小时
        max_use_count: 10
        cache_scope: "domain"

  # 请求URL动作序列
  webhook_handler:
    sequence:
      - type: "request_url"
        url: "https://api.example.com/webhook"
        method: "POST"
        timeout_ms: 5000
        body_type: "json"
        headers: "Content-Type:application/json,Authorization:Bearer token123"

  # 空响应动作序列
  block_handler:
    sequence:
      - type: "null_response"
        status_code: 403
        content_type: "application/json"
        body: '{"error": "Access denied"}'

  # 日志记录动作序列
  log_handler:
    sequence:
      - type: "log"
        message: "触发器事件已执行"
        level: "info"

  # 请求阻止动作序列
  block_request_handler:
    sequence:
      - type: "block_request"
        reason: "恶意请求被阻止"

  # 请求修改动作序列
  modify_request_handler:
    sequence:
      - type: "modify_request"
        headers:
          "X-Modified": "true"
          "User-Agent": "FlexProxy/1.0"
        remove_headers: ["X-Real-IP"]

  # 响应修改动作序列
  modify_response_handler:
    sequence:
      - type: "modify_response"
        headers:
          "X-Proxy-Modified": "true"
          "Cache-Control": "no-cache"
        status_code: 200

  # 响应缓存动作序列
  cache_response_handler:
    sequence:
      - type: "cache_response"
        duration: 1800
        cache_key: "response_{{.url_hash}}"

  # 脚本执行动作序列
  script_handler:
    sequence:
      - type: "script"
        script: "console.log('Custom script executed');"
        language: "javascript"

  # 域名封禁动作序列（修正：ban_domain不在验证器支持列表中，使用banip替代）
  ban_domain_handler:
    sequence:
      - type: "banip"  # 使用支持的动作类型
        duration: 3600
      - type: "log"
        message: "域名相关IP已被封禁: {{.request_domain}}"
        level: "warn"

  # 告警动作序列（修正：alert不支持，使用log替代）
  alert_handler:
    sequence:
      - type: "log"  # 使用支持的动作类型替代alert
        message: "🚨 系统告警: 检测到异常行为"
        level: "error"
      - type: "request_url"  # 发送告警到监控系统
        url: "https://monitor.example.com/alert"
        method: "POST"
        body: '{"level": "warning", "message": "检测到异常行为", "timestamp": "{{.timestamp}}"}'
        body_type: "json"
        timeout_ms: 3000

  # 重定向动作序列（修正：redirect不支持，使用null_response返回重定向响应）
  redirect_handler:
    sequence:
      - type: "null_response"  # 使用支持的动作类型
        status_code: 302
        content_type: "text/html"
        body: '<html><head><meta http-equiv="refresh" content="0;url=https://example.com/blocked"></head></html>'
        headers: "Location:https://example.com/blocked"

  # 限流动作序列（修正：rate_limit不支持，使用组合动作实现限流效果）
  rate_limit_handler:
    sequence:
      - type: "cache"  # 使用缓存记录访问频率
        duration: 60
        cache_scope: "global"
        custom_key: "rate_limit_{{.client_ip}}"
        max_use_count: 10
      - type: "log"
        message: "客户端访问频率限制: {{.client_ip}}"
        level: "warn"

  # Webhook通知动作序列（修正：webhook不支持，使用request_url替代）
  webhook_notification_handler:
    sequence:
      - type: "request_url"  # 使用支持的动作类型
        url: "https://hooks.slack.com/services/xxx"
        method: "POST"
        body: '{"text": "FlexProxy Alert: {{.message}}"}'
        body_type: "json"
        headers: "Content-Type:application/json"
        timeout_ms: 5000

  # 邮件通知动作序列（修正：email不支持，使用request_url发送到邮件API）
  email_notification_handler:
    sequence:
      - type: "request_url"  # 使用支持的动作类型
        url: "https://api.mailgun.com/v3/your-domain/messages"
        method: "POST"
        body: 'to=<EMAIL>&subject=FlexProxy Alert&text=检测到异常行为: {{.details}}'
        body_type: "form"
        headers: "Authorization:Basic {{.mailgun_api_key}}"
        timeout_ms: 10000
      - type: "log"
        message: "邮件告警已发送: {{.details}}"
        level: "info"

  # 自定义动作序列（修正：custom不支持，使用script替代）
  custom_handler:
    sequence:
      - type: "script"  # 使用支持的动作类型
        script: |
          // 自定义业务逻辑处理
          console.log('处理特殊情况:', opt.request_data);
          // 返回处理结果
          return true;
        language: "javascript"
      - type: "log"
        message: "自定义脚本处理完成"
        level: "info"

  # 高级请求URL动作序列（展示完整参数）
  advanced_request_url_handler:
    sequence:
      - type: "request_url"
        url: "https://api.example.com/webhook"
        method: "POST"
        body: '{"event": "proxy_action", "data": "{{.request_data}}", "timestamp": "{{.timestamp}}", "client_ip": "{{.client_ip}}"}'
        body_type: "json"
        headers: "Content-Type:application/json,Authorization:Bearer {{.token}},X-FlexProxy-Version:1.0,X-Request-Source:FlexProxy"
        timeout_ms: 10000
        proxy_option: "quality:high"
        follow_redirect: true
        max_redirects: 5
        retry_count: 3
        retry_delay_ms: 1000
        validate_ssl: true
        user_agent: "FlexProxy/1.0 (Advanced Request Handler)"
        copy_headers: true
        save_response: true
        save_response_path: "/tmp/webhook_responses/"
        validate_status_codes: [200, 201, 202]
        extract_headers:
          "X-Request-ID": "webhook_request_id"
          "X-Response-Time": "webhook_response_time"
          "X-Rate-Limit-Remaining": "rate_limit_remaining"

  # 完整参数的缓存动作序列
  comprehensive_cache_handler:
    sequence:
      - type: "cache"
        duration: 3600
        max_use_count: 100
        cache_scope: "global"  # global, domain, url
        custom_key: "comprehensive_{{.domain}}_{{.url_hash}}_{{.method}}_{{.user_agent_hash}}"
        ignore_params: false  # 是否忽略URL参数

  # 高级缓存动作序列
  advanced_cache_handler:
    sequence:
      - type: "cache"
        duration: 3600
        max_use_count: 100
        cache_scope: "global"
        custom_key: "advanced_cache_{{.domain}}_{{.url_hash}}"
        ignore_params: false

  # 绕过代理动作序列
  bypass_proxy_handler:
    sequence:
      - type: "bypass_proxy"
        timeout_ms: 15000

  # 保存到池动作序列
  save_to_pool_handler:
    sequence:
      - type: "save_to_pool"
        pool_name: "quality_proxies"
        quality_score: 85
        tags: ["fast", "reliable"]

  # 重试相同IP动作序列
  retry_same_handler:
    sequence:
      - type: "retry_same"
        retry_count: 2

  # 域名级别IP封禁动作序列（完整参数配置）
  banipdomain_handler:
    sequence:
      - type: "banipdomain"
        duration: 3600  # 封禁1小时
        scope: "domain"  # 封禁范围：url, domain, global
      - type: "log"
        message: "域名级别IP封禁已执行: {{.domain}} - IP: {{.client_ip}}"
        level: "warn"

  # 高级域名级别IP封禁动作序列（多级封禁策略）
  advanced_banipdomain_handler:
    sequence:
      - type: "banipdomain"
        duration: 1800  # 首次封禁30分钟
        scope: "url"     # 仅针对特定URL
      - type: "log"
        message: "URL级别IP封禁: {{.url}} - IP: {{.client_ip}}"
        level: "info"
    relation:  # 关联动作序列
      - type: "banipdomain"
        duration: 7200   # 如果重复违规，封禁2小时
        scope: "domain"  # 升级到域名级别
      - type: "log"
        message: "升级到域名级别封禁: {{.domain}} - IP: {{.client_ip}}"
        level: "error"

  # 阻止动作序列（使用支持的动作类型）
  block_handler_v2:
    sequence:
      - type: "block_request"  # 使用支持的类型
        reason: "请求被阻止"
      - type: "log"
        message: "请求已被阻止: 安全策略违规"
        level: "warn"

  # Slack通知动作序列（使用支持的动作类型替代）
  slack_notification_handler:
    sequence:
      - type: "log"  # 使用支持的类型替代slack
        message: "🚨 代理异常检测到 - 应发送Slack通知到#alerts频道"
        level: "error"
      # 注意：实际的slack动作类型需要架构改进后才能使用
      # 当前配置仅记录日志，实际Slack通知功能需要实现对应的Executor

  # Discord通知动作序列（使用支持的动作类型替代）
  discord_notification_handler:
    sequence:
      - type: "request_url"  # 使用支持的类型替代discord
        url: "https://discord.com/api/webhooks/your-webhook-url"
        method: "POST"
        body: '{"content": "🚨 FlexProxy系统警报: {{.message}}"}'
        body_type: "json"
        headers: "Content-Type:application/json"
        timeout_ms: 5000
      - type: "log"
        message: "Discord通知已发送: {{.message}}"
        level: "info"

  # 完整的请求修改动作序列
  comprehensive_modify_request_handler:
    sequence:
      - type: "modify_request"
        headers:
          "X-Forwarded-By": "FlexProxy"
          "X-Request-ID": "{{.request_id}}"
          "User-Agent": "FlexProxy-Client/1.0"
          "Accept-Encoding": "gzip, deflate, br"
        remove_headers: ["X-Real-IP", "X-Original-IP"]
        query_params:
          "proxy": "true"
          "timestamp": "{{.timestamp}}"
        remove_query_params: ["debug", "test"]

  # 完整的响应修改动作序列
  comprehensive_modify_response_handler:
    sequence:
      - type: "modify_response"
        headers:
          "X-Proxy-Server": "FlexProxy"
          "X-Response-Time": "{{.response_time}}ms"
          "Cache-Control": "public, max-age=3600"
          "X-Content-Filtered": "true"
        remove_headers: ["Server", "X-Powered-By"]
        status_code: 200

  # 高级缓存响应动作序列
  advanced_cache_response_handler:
    sequence:
      - type: "cache_response"
        duration: 7200  # 2小时
        cache_key: "advanced_{{.domain}}_{{.url_hash}}_{{.method}}"
        conditions:
          status_codes: [200, 201, 202]
          content_types: ["application/json", "text/html", "application/xml"]
        headers:
          "X-Cache-Status": "HIT"
          "X-Cache-TTL": "7200"

  # 脚本执行动作序列（展示完整脚本功能）
  advanced_script_handler:
    sequence:
      - type: "script"
        script: |
          // 高级脚本处理逻辑
          const url = opt.url || '';
          const method = opt.method || 'GET';
          const status = opt.status || 0;

          // 检查是否为API请求
          if (url.includes('/api/')) {
            console.log('API请求检测:', url);
            // 可以设置特殊处理标志
            opt.api_request = true;
          }

          // 检查响应状态
          if (status >= 400) {
            console.log('错误状态码检测:', status);
            opt.error_detected = true;
          }

          // 返回处理结果
          return opt.api_request || opt.error_detected;
        language: "javascript"
        timeout_ms: 2000
      - type: "log"
        message: "高级脚本处理完成，结果: {{.script_result}}"
        level: "info"

  # 智能质量评估和代理池管理动作序列
  intelligent_proxy_management_handler:
    sequence:
      - type: "save_to_pool"
        pool_name: "high_quality_proxies"
        quality_score: 90
        tags: ["fast", "reliable", "premium"]
      - type: "script"
        script: |
          // 智能代理质量评估
          const responseTime = opt.response_time || 0;
          const successRate = opt.success_rate || 0;
          const errorCount = opt.error_count || 0;

          // 计算质量分数
          let qualityScore = 100;
          if (responseTime > 5000) qualityScore -= 20;
          if (responseTime > 10000) qualityScore -= 30;
          if (successRate < 0.9) qualityScore -= 25;
          if (errorCount > 5) qualityScore -= 15;

          // 设置代理池分类
          if (qualityScore >= 85) {
            opt.pool_category = "premium";
          } else if (qualityScore >= 70) {
            opt.pool_category = "standard";
          } else {
            opt.pool_category = "backup";
          }

          console.log('代理质量评估完成:', qualityScore, opt.pool_category);
          return qualityScore >= 60; // 低于60分的代理不保存
        language: "javascript"
        timeout_ms: 1000

  # 多层安全检测动作序列
  multi_layer_security_handler:
    sequence:
      - type: "script"
        script: |
          // 第一层：基础安全检查
          const url = opt.url || '';
          const userAgent = opt.user_agent || '';
          const clientIP = opt.client_ip || '';

          // 检查恶意模式
          const maliciousPatterns = [
            /\.\.\//g,  // 路径遍历
            /<script/gi, // XSS
            /union.*select/gi, // SQL注入
            /cmd=|exec=/gi // 命令注入
          ];

          let threatLevel = 0;
          maliciousPatterns.forEach(pattern => {
            if (pattern.test(url)) threatLevel += 25;
          });

          // 检查可疑User-Agent
          const suspiciousUA = ['sqlmap', 'nikto', 'nmap', 'masscan'];
          if (suspiciousUA.some(ua => userAgent.toLowerCase().includes(ua))) {
            threatLevel += 30;
          }

          opt.threat_level = threatLevel;
          opt.security_action = threatLevel > 25 ? 'block' : 'monitor';

          return threatLevel > 0;
        language: "javascript"
      - type: "log"
        message: "安全威胁检测: 威胁等级={{.threat_level}}, 建议动作={{.security_action}}"
        level: "warn"
      - type: "banip"
        duration: 3600  # 高威胁IP封禁1小时

  # 智能缓存策略动作序列
  intelligent_caching_handler:
    sequence:
      - type: "script"
        script: |
          // 智能缓存决策
          const contentType = opt.content_type || '';
          const statusCode = opt.status_code || 0;
          const responseSize = opt.response_size || 0;
          const url = opt.url || '';

          // 根据内容类型决定缓存策略
          let cacheDuration = 0;
          let cacheScope = 'url';

          if (contentType.includes('application/json')) {
            cacheDuration = url.includes('/api/') ? 300 : 1800; // API数据缓存5分钟，其他30分钟
            cacheScope = 'domain';
          } else if (contentType.includes('text/html')) {
            cacheDuration = 600; // HTML缓存10分钟
            cacheScope = 'url';
          } else if (contentType.match(/image|css|js/)) {
            cacheDuration = 86400; // 静态资源缓存24小时
            cacheScope = 'global';
          }

          // 根据响应大小调整策略
          if (responseSize > 1024 * 1024) { // 大于1MB
            cacheDuration = Math.min(cacheDuration, 3600); // 最多缓存1小时
          }

          opt.cache_duration = cacheDuration;
          opt.cache_scope = cacheScope;
          opt.should_cache = cacheDuration > 0 && statusCode === 200;

          return opt.should_cache;
        language: "javascript"
      - type: "cache_response"
        duration: "{{.cache_duration}}"
        cache_key: "intelligent_{{.domain}}_{{.url_hash}}"
        conditions:
          status_codes: [200, 201, 202]
          content_types: ["application/json", "text/html", "text/css", "application/javascript"]

# ================================
# 事件配置
# ================================
# 定义各种触发条件和对应的处理动作
events:
  # 状态码触发事件
  - name: "status_code_handler"
    enable: true
    trigger_type: "status"
    process_stage: "post_header"
    priority: 10
    dns_cache_ttl: 300
    dns_lookup_mode: "custom"
    dns_no_cache: false
    custom_dns_servers:
      - server: "*******:53"  # IP:端口格式（现在支持）
        protocol: "udp"
        timeout: 3000
        priority: 1
        tags: ["google", "event_specific"]
    http_proxy_dns: "*******:53"  # HTTPProxyDNS支持端口格式

    # 条件定义
    conditions:
      - name: "error_status"
        enable: true
        status_codes:
          codes: [403, 404, 500, 502, 503]
          relation: "or"
        condition_relation: "AND"

    # 匹配规则
    matches:
      - name: "error_match"
        enable: true
        conditions: ["error_status"]
        logic: "AND"
        actions:
          - type: "banip"
            duration: 600
          - type: "retry"
            retry_count: 3

  # 响应体内容触发事件
  - name: "body_content_handler"
    enable: true
    trigger_type: "body"
    process_stage: "post_body"
    priority: 20

    conditions:
      - name: "error_content"
        enable: true
        body_patterns:
          patterns:
            - pattern: "error|failed|denied"
              type: "regex"
              weight: 10
            - pattern: "blocked"
              type: "string"
              weight: 5
          relation: "or"
          case_sensitive: false
          timeout: 1000
          max_matches: 5

    matches:
      - name: "content_error_match"
        enable: true
        conditions: ["error_content"]
        actions:
          - type: "banipdomain"
            duration: 1800
          - type: "cache"
            duration: 300
            cache_scope: "url"

  # 请求时间触发事件
  - name: "request_time_handler"
    enable: true
    trigger_type: "max_request_time"
    process_stage: "post_body"
    priority: 15

    conditions:
      - name: "slow_request"
        enable: true
        max_request_time: 10000  # 10秒
      - name: "very_slow_request"
        enable: true
        max_request_time: 30000  # 30秒

    matches:
      - name: "timeout_match"
        enable: true
        conditions: ["slow_request"]
        logic: "AND"
        actions:
          - type: "retry_same"
            retry_count: 1
      - name: "severe_timeout_match"
        enable: true
        conditions: ["very_slow_request"]
        logic: "AND"
        actions:
          - type: "banip"
            duration: 3600
          - type: "save_to_pool"
            pool_name: "slow_ips"

  # URL模式触发事件
  - name: "url_pattern_handler"
    enable: true
    trigger_type: "url"
    process_stage: "pre"
    priority: 5

    conditions:
      - name: "api_endpoints"
        enable: true
        url_patterns:
          patterns:
            - pattern: "/api/v[0-9]+/"
              type: "regex"
              weight: 10
            - pattern: "/admin/"
              type: "string"
              weight: 15
          relation: "or"
          case_sensitive: true
      - name: "static_resources"
        enable: true
        url_patterns:
          patterns:
            - pattern: "\\.(css|js|png|jpg|gif)$"
              type: "regex"
              weight: 5
          relation: "or"
          case_sensitive: false

    matches:
      - name: "api_match"
        enable: true
        conditions: ["api_endpoints"]
        actions:
          - type: "cache"
            duration: 1800
            cache_scope: "domain"
      - name: "static_match"
        enable: true
        conditions: ["static_resources"]
        actions:
          - type: "bypass_proxy"

  # 域名模式触发事件
  - name: "domain_handler"
    enable: true
    trigger_type: "domain"
    process_stage: "pre"
    priority: 8

    conditions:
      - name: "cdn_domains"
        enable: true
        domain_patterns:
          patterns:
            - pattern: ".*\\.cdn\\."
              type: "regex"
              weight: 10
            - pattern: "cloudflare.com"
              type: "string"
              weight: 8
          relation: "or"
          case_sensitive: false

    matches:
      - name: "cdn_match"
        enable: true
        conditions: ["cdn_domains"]
        actions:
          - type: "bypass_proxy"
          - type: "cache"
            duration: 7200
            cache_scope: "global"

  # 请求头触发事件
  - name: "request_header_handler"
    enable: true
    trigger_type: "request_header"
    process_stage: "pre"
    priority: 12

    conditions:
      - name: "user_agent_check"
        enable: true
        request_header_patterns:
          headers:
            "User-Agent":
              patterns:
                - pattern: "bot|crawler|spider"
                  type: "regex"
                  weight: 10
              relation: "or"
              case_sensitive: false
            "X-Forwarded-For":
              patterns:
                - pattern: "^192\\.168\\."
                  type: "regex"
                  weight: 5
              relation: "or"

    matches:
      - name: "bot_match"
        enable: true
        conditions: ["user_agent_check"]
        actions:
          - type: "null_response"
            status_code: 429
            content_type: "text/plain"
            body: "Rate limited"

  # 响应头触发事件
  - name: "response_header_handler"
    enable: true
    trigger_type: "response_header"
    process_stage: "post_header"
    priority: 18

    conditions:
      - name: "server_error_headers"
        enable: true
        response_header_patterns:
          headers:
            "X-Error-Code":
              patterns:
                - pattern: "AUTH_FAILED|RATE_LIMITED"
                  type: "regex"
                  weight: 15
              relation: "or"
            "Retry-After":
              patterns:
                - pattern: ".*"
                  type: "regex"
                  weight: 10
              relation: "or"

    matches:
      - name: "server_error_match"
        enable: true
        conditions: ["server_error_headers"]
        actions:
          - type: "banip"
            duration: 1800
          - type: "request_url"
            url: "https://monitor.example.com/alert"
            method: "POST"
            body_type: "json"
            timeout_ms: 3000

  # 连接超时触发事件
  - name: "connection_timeout_handler"
    enable: true
    trigger_type: "conn_time_out"
    process_stage: "post_body"
    priority: 25

    conditions:
      - name: "connection_timeout"
        enable: true
        connection_timeout: 5000  # 5秒连接超时

    matches:
      - name: "timeout_match"
        enable: true
        conditions: ["connection_timeout"]
        actions:
          - type: "retry"
            retry_count: 2
          - type: "save_to_pool"
            pool_name: "timeout_proxies"

  # 组合条件触发事件（复杂逻辑）
  - name: "combined_conditions_handler"
    enable: true
    trigger_type: "combined"
    process_stage: "post_body"
    priority: 30

    conditions:
      - name: "high_latency"
        enable: true
        max_request_time: 8000
      - name: "error_status"
        enable: true
        status_codes:
          codes: [500, 502, 503]
          relation: "or"
      - name: "error_body"
        enable: true
        body_patterns:
          patterns:
            - pattern: "internal server error"
              type: "string"
              weight: 10
          relation: "or"
          case_sensitive: false

    matches:
      # 高延迟且状态码错误
      - name: "latency_and_status_error"
        enable: true
        conditions: ["high_latency", "error_status"]
        logic: "AND"
        actions:
          - type: "banip"
            duration: 3600
          - type: "retry"
            retry_count: 1

      # 状态码错误或响应体错误
      - name: "status_or_body_error"
        enable: true
        conditions: ["error_status", "error_body"]
        logic: "OR"
        actions:
          - type: "retry_same"
            retry_count: 2

      # 嵌套匹配规则示例
      - name: "complex_nested_match"
        enable: true
        conditions: ["high_latency"]
        logic: "AND"
        sub_matches:
          - name: "nested_error_check"
            enable: true
            conditions: ["error_status", "error_body"]
            logic: "OR"
            actions:
              - type: "banipdomain"
                duration: 7200
        actions:
          - type: "cache"
            duration: 600
            cache_scope: "url"

  # 自定义触发器事件
  - name: "custom_trigger_handler"
    enable: true
    trigger_type: "custom"
    process_stage: "post_body"
    priority: 35

    conditions:
      - name: "custom_condition"
        enable: true
        # 自定义条件可以通过trigger_id引用特定的触发器实现
        trigger_id: "custom_business_logic"
        condition_relation: "AND"

    matches:
      - name: "custom_match"
        enable: true
        conditions: ["custom_condition"]
        actions:
          - type: "request_url"
            url: "https://webhook.example.com/custom"
            method: "POST"
            body: '{"event": "custom_trigger", "timestamp": "{{.timestamp}}"}'
            body_type: "json"
            headers: "Content-Type:application/json"
            timeout_ms: 10000
            follow_redirect: true
            max_redirects: 3
            retry_count: 2
            retry_delay_ms: 1000
            validate_ssl: true
            copy_headers: false
            save_response: true
            save_response_path: "/tmp/custom_responses/"
            validate_status_codes: [200, 201, 202]

  # 最小请求时间触发事件（检测异常快速响应）
  - name: "min_request_time_handler"
    enable: true
    trigger_type: "min_request_time"
    process_stage: "post_body"
    priority: 22

    conditions:
      - name: "too_fast_response"
        enable: true
        min_request_time: 100  # 响应时间少于100ms可能异常

    matches:
      - name: "suspicious_fast_response"
        enable: true
        conditions: ["too_fast_response"]
        actions:
          - type: "cache"
            duration: 60  # 短时间缓存
            cache_scope: "url"
            custom_key: "fast_response_{{.url_hash}}"
          - type: "request_url"
            url: "https://analytics.example.com/fast-response"
            method: "POST"
            body_type: "json"

  # 高级模式匹配事件（展示链式模式和复杂模式配置）
  - name: "advanced_pattern_handler"
    enable: true
    trigger_type: "body"
    process_stage: "post_body"
    priority: 40

    conditions:
      - name: "complex_body_analysis"
        enable: true
        body_patterns:
          # 基础模式列表
          patterns:
            - pattern: "\\{.*\"error\".*\\}"
              type: "regex"
              weight: 15
              logic: "AND"
              negate: false
              # 子组模式
              subgroup:
                patterns:
                  - pattern: "\"code\":\\s*[45]\\d{2}"
                    type: "regex"
                    weight: 10
                relation: "and"
                case_sensitive: false
            - pattern: "access denied"
              type: "string"
              weight: 12
              logic: "OR"
              # 链式模式
              chain:
                - order: 1
                  pattern: "permission"
                  operator: "and"
                  negate: false
                - order: 2
                  pattern: "insufficient"
                  operator: "or"
                  negate: false
          relation: "or"
          # 链式模式配置
          chained:
            - order: 1
              pattern: "first_check_.*"
              operator: "and"
              negate: false
            - order: 2
              pattern: "second_validation"
              operator: "and"
              negate: false
          negation: false
          case_sensitive: false
          multiline: true
          dotall: true
          unicode: true
          timeout: 2000
          max_matches: 10

    matches:
      - name: "advanced_pattern_match"
        enable: true
        conditions: ["complex_body_analysis"]
        actions:
          - type: "banip"
            duration: 1800
          - type: "cache"
            duration: 300
            cache_scope: "domain"
            ignore_params: true

  # 请求体模式触发事件
  - name: "request_body_handler"
    enable: true
    trigger_type: "request_body"
    process_stage: "pre"
    priority: 6

    conditions:
      - name: "malicious_payload"
        enable: true
        request_body_patterns:
          patterns:
            - pattern: "<script|javascript:|vbscript:"
              type: "regex"
              weight: 20
            - pattern: "union.*select|drop.*table"
              type: "regex"
              weight: 25
            - pattern: "../../../"
              type: "string"
              weight: 15
          relation: "or"
          case_sensitive: false
          timeout: 1500

    matches:
      - name: "security_threat_match"
        enable: true
        conditions: ["malicious_payload"]
        actions:
          - type: "null_response"
            status_code: 403
            content_type: "application/json"
            body: '{"error": "Malicious request detected", "code": 403}'
            custom_headers: "X-Security-Block:true,X-Threat-Level:high"
          - type: "banip"
            duration: 86400  # 24小时封禁

  # 多阶段处理事件（展示不同处理阶段的配置）
  - name: "multi_stage_handler"
    enable: true
    trigger_type: "combined"
    process_stage: "pre"  # 预处理阶段
    priority: 3

    conditions:
      - name: "pre_check"
        enable: true
        url_patterns:
          patterns:
            - pattern: "/sensitive/"
              type: "string"
              weight: 10
          relation: "or"

    matches:
      - name: "pre_stage_match"
        enable: true
        conditions: ["pre_check"]
        actions:
          - type: "request_url"
            url: "https://auth.example.com/validate"
            method: "GET"
            timeout_ms: 2000
            proxy_option: "none"  # 不使用代理进行验证

  # DNS配置增强事件
  - name: "dns_enhanced_handler"
    enable: true
    trigger_type: "domain"
    process_stage: "pre"
    priority: 2
    dns_cache_ttl: 600
    dns_lookup_mode: "hybrid"  # 混合模式
    dns_no_cache: false
    custom_dns_servers:
      - server: "*******:53"  # Google DNS (IP:端口格式)
        protocol: "udp"
        timeout: 2000
        priority: 1
        tags: ["google", "primary"]
      - server: "*******"  # Cloudflare DNS-over-TLS
        protocol: "dot"  # 使用DoT协议
        timeout: 3000
        priority: 2
        tags: ["cloudflare", "secure"]
      - server: "**************"  # OpenDNS (纯IP地址)
        protocol: "udp"
        timeout: 2500
        priority: 3
        tags: ["opendns"]
    http_proxy_dns: "*******:53"  # Quad9 with port (HTTPProxyDNS支持端口)

    conditions:
      - name: "special_domains"
        enable: true
        domain_patterns:
          patterns:
            - pattern: ".*\\.internal$"
              type: "regex"
              weight: 10
            - pattern: "localhost"
              type: "string"
              weight: 15
          relation: "or"

    matches:
      - name: "internal_domain_match"
        enable: true
        conditions: ["special_domains"]
        actions:
          - type: "bypass_proxy"

  # 向后兼容的条件动作配置示例（已废弃但仍支持）
  - name: "legacy_conditional_actions"
    enable: false  # 默认禁用，仅作示例
    trigger_type: "status"
    process_stage: "post_header"
    priority: 50

    # 使用已废弃的conditional_actions配置
    conditional_actions:
      - condition_name: "legacy_error_condition"
        enable: true
        status_codes:
          codes: [404, 500]
          relation: "or"
        body_patterns:
          patterns:
            - pattern: "not found"
              type: "string"
              weight: 10
          relation: "or"
          case_sensitive: false
        condition_relation: "OR"
        action_sequence_name: "status_handler"  # 引用已定义的动作序列

  # 安全阻止事件（使用新的block动作类型）
  - name: "security_block_handler"
    enable: true
    trigger_type: "combined"
    process_stage: "pre"
    priority: 1  # 最高优先级

    conditions:
      - name: "malicious_patterns"
        enable: true
        url_patterns:
          patterns:
            - pattern: "\\.\\./"  # 路径遍历攻击
              type: "regex"
              weight: 20
            - pattern: "<script"  # XSS攻击
              type: "string"
              weight: 20
            - pattern: "union.*select"  # SQL注入
              type: "regex"
              weight: 20
          relation: "or"
          case_sensitive: false

    matches:
      - name: "security_threat_match"
        enable: true
        conditions: ["malicious_patterns"]
        action_sequence_name: "block_handler_v2"  # 引用修正后的动作序列

  # Slack通知事件（使用新的slack动作类型）
  - name: "slack_alert_handler"
    enable: true
    trigger_type: "status"
    process_stage: "post_header"
    priority: 15

    conditions:
      - name: "critical_errors"
        enable: true
        status_codes:
          codes: [500, 502, 503, 504]
          relation: "or"

    matches:
      - name: "critical_error_match"
        enable: true
        conditions: ["critical_errors"]
        action_sequence_name: "slack_notification_handler"  # 引用修正后的动作序列

  # Discord通知事件（使用新的discord动作类型）
  - name: "discord_notification_handler"
    enable: true
    trigger_type: "max_request_time"
    process_stage: "post_body"
    priority: 18

    conditions:
      - name: "slow_response"
        enable: true
        max_request_time: 10000  # 10秒

    matches:
      - name: "slow_response_match"
        enable: true
        conditions: ["slow_response"]
        action_sequence_name: "discord_notification_handler"  # 引用修正后的动作序列

  # 高级请求修改事件
  - name: "advanced_request_modification_handler"
    enable: true
    trigger_type: "url"
    process_stage: "pre"
    priority: 4

    conditions:
      - name: "api_requests"
        enable: true
        url_patterns:
          patterns:
            - pattern: "/api/v[0-9]+/"
              type: "regex"
              weight: 10
            - pattern: "/graphql"
              type: "string"
              weight: 8
          relation: "or"
          case_sensitive: false

    matches:
      - name: "api_modification_match"
        enable: true
        conditions: ["api_requests"]
        action_sequence_name: "comprehensive_modify_request_handler"

  # 高级响应修改事件
  - name: "advanced_response_modification_handler"
    enable: true
    trigger_type: "status"
    process_stage: "post_header"
    priority: 16

    conditions:
      - name: "success_responses"
        enable: true
        status_codes:
          codes: [200, 201, 202]
          relation: "or"

    matches:
      - name: "success_modification_match"
        enable: true
        conditions: ["success_responses"]
        action_sequence_name: "comprehensive_modify_response_handler"

  # 高级缓存响应事件
  - name: "advanced_cache_response_handler"
    enable: true
    trigger_type: "combined"
    process_stage: "post_body"
    priority: 14

    conditions:
      - name: "cacheable_content"
        enable: true
        status_codes:
          codes: [200, 201, 202]
          relation: "or"
      - name: "static_content_types"
        enable: true
        response_header_patterns:
          headers:
            "Content-Type":
              patterns:
                - pattern: "application/json|text/html|application/xml"
                  type: "regex"
                  weight: 10
              relation: "or"

    matches:
      - name: "advanced_cache_match"
        enable: true
        conditions: ["cacheable_content", "static_content_types"]
        logic: "AND"
        action_sequence_name: "advanced_cache_response_handler"

  # 高级脚本处理事件
  - name: "advanced_script_processing_handler"
    enable: true
    trigger_type: "combined"
    process_stage: "post_body"
    priority: 25

    conditions:
      - name: "complex_processing_needed"
        enable: true
        url_patterns:
          patterns:
            - pattern: "/api/"
              type: "string"
              weight: 5
          relation: "or"
      - name: "error_or_slow_response"
        enable: true
        status_codes:
          codes: [400, 401, 403, 404, 500, 502, 503]
          relation: "or"
        condition_relation: "OR"
        max_request_time: 5000

    matches:
      - name: "script_processing_match"
        enable: true
        conditions: ["complex_processing_needed", "error_or_slow_response"]
        logic: "OR"
        action_sequence_name: "advanced_script_handler"

  # 智能代理质量管理事件
  - name: "intelligent_proxy_quality_handler"
    enable: true
    trigger_type: "combined"
    process_stage: "post_body"
    priority: 30

    conditions:
      - name: "successful_response"
        enable: true
        status_codes:
          codes: [200, 201, 202, 204]
          relation: "or"
      - name: "fast_response"
        enable: true
        max_request_time: 3000  # 3秒内响应
      - name: "quality_domains"
        enable: true
        domain_patterns:
          patterns:
            - pattern: ".*\\.(com|org|net)$"
              type: "regex"
              weight: 10
          relation: "or"

    matches:
      - name: "high_quality_proxy_match"
        enable: true
        conditions: ["successful_response", "fast_response", "quality_domains"]
        logic: "AND"
        action_sequence_name: "intelligent_proxy_management_handler"

  # 多层安全防护事件
  - name: "multi_layer_security_handler"
    enable: true
    trigger_type: "combined"
    process_stage: "pre"
    priority: 1  # 最高优先级

    conditions:
      - name: "high_risk_patterns"
        enable: true
        url_patterns:
          patterns:
            - pattern: "\\.\\./"  # 路径遍历
              type: "regex"
              weight: 30
            - pattern: "<script|javascript:|vbscript:|onload=|onerror="
              type: "regex"
              weight: 30
            - pattern: "union.*select|drop.*table|insert.*into|delete.*from"
              type: "regex"
              weight: 30
            - pattern: "cmd=|exec=|system=|eval\\(|base64_decode"
              type: "regex"
              weight: 25
          relation: "or"
          case_sensitive: false
      - name: "malicious_user_agents"
        enable: true
        request_header_patterns:
          headers:
            "User-Agent":
              patterns:
                - pattern: "sqlmap|nikto|nmap|masscan|dirb|gobuster|wfuzz"
                  type: "regex"
                  weight: 25
                - pattern: "python-requests|curl|wget"
                  type: "regex"
                  weight: 10
              relation: "or"
              case_sensitive: false
      - name: "suspicious_request_patterns"
        enable: true
        request_body_patterns:
          patterns:
            - pattern: "password.*=.*admin|username.*=.*admin"
              type: "regex"
              weight: 15
            - pattern: "\\{.*\\$.*\\}"  # 模板注入
              type: "regex"
              weight: 20
          relation: "or"
          case_sensitive: false

    matches:
      - name: "critical_security_threat"
        enable: true
        conditions: ["high_risk_patterns"]
        actions:
          - type: "null_response"
            status_code: 403
            content_type: "application/json"
            body: '{"error": "Security threat detected", "code": 403, "timestamp": "{{.timestamp}}"}'
          - type: "banip"
            duration: 86400  # 24小时封禁
          - type: "log"
            message: "🚨 严重安全威胁已阻止: {{.url}} from {{.client_ip}} - Pattern: {{.matched_pattern}}"
            level: "error"
      - name: "moderate_security_threat"
        enable: true
        conditions: ["malicious_user_agents", "suspicious_request_patterns"]
        logic: "OR"
        action_sequence_name: "multi_layer_security_handler"

  # 智能缓存优化事件
  - name: "intelligent_caching_optimization_handler"
    enable: true
    trigger_type: "combined"
    process_stage: "post_body"
    priority: 15

    conditions:
      - name: "cacheable_responses"
        enable: true
        status_codes:
          codes: [200, 201, 202]
          relation: "or"
      - name: "cacheable_content_types"
        enable: true
        response_header_patterns:
          headers:
            "Content-Type":
              patterns:
                - pattern: "application/json|text/html|text/css|application/javascript|image/"
                  type: "regex"
                  weight: 10
              relation: "or"
      - name: "reasonable_size"
        enable: true
        response_header_patterns:
          headers:
            "Content-Length":
              patterns:
                - pattern: "^[0-9]{1,7}$"  # 小于10MB
                  type: "regex"
                  weight: 5
              relation: "or"

    matches:
      - name: "intelligent_cache_match"
        enable: true
        conditions: ["cacheable_responses", "cacheable_content_types", "reasonable_size"]
        logic: "AND"
        action_sequence_name: "intelligent_caching_handler"

  # 安全威胁检测事件（综合安全检查）
  - name: "comprehensive_security_handler"
    enable: true
    trigger_type: "combined"
    process_stage: "pre"
    priority: 1  # 最高优先级

    conditions:
      - name: "malicious_url_patterns"
        enable: true
        url_patterns:
          patterns:
            - pattern: "\\.\\./"  # 路径遍历
              type: "regex"
              weight: 25
            - pattern: "<script|javascript:|vbscript:"  # XSS
              type: "regex"
              weight: 25
            - pattern: "union.*select|drop.*table|insert.*into"  # SQL注入
              type: "regex"
              weight: 25
            - pattern: "cmd=|exec=|system="  # 命令注入
              type: "regex"
              weight: 20
          relation: "or"
          case_sensitive: false
      - name: "suspicious_headers"
        enable: true
        request_header_patterns:
          headers:
            "User-Agent":
              patterns:
                - pattern: "sqlmap|nikto|nmap|masscan"
                  type: "regex"
                  weight: 20
              relation: "or"
              case_sensitive: false
            "X-Forwarded-For":
              patterns:
                - pattern: "^(10\\.|192\\.168\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.)"
                  type: "regex"
                  weight: 5
              relation: "or"

    matches:
      - name: "security_threat_match"
        enable: true
        conditions: ["malicious_url_patterns", "suspicious_headers"]
        logic: "OR"
        actions:
          - type: "null_response"
            status_code: 403
            content_type: "application/json"
            body: '{"error": "Security threat detected", "code": 403}'
          - type: "banip"
            duration: 86400  # 24小时封禁
          - type: "log"
            message: "🚨 安全威胁已阻止: {{.url}} from {{.client_ip}}"
            level: "error"

  # 性能优化事件（智能缓存和代理选择）
  - name: "performance_optimization_handler"
    enable: true
    trigger_type: "combined"
    process_stage: "pre"
    priority: 7

    conditions:
      - name: "static_resources"
        enable: true
        url_patterns:
          patterns:
            - pattern: "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$"
              type: "regex"
              weight: 10
            - pattern: "/static/|/assets/|/public/"
              type: "regex"
              weight: 8
          relation: "or"
          case_sensitive: false
      - name: "cdn_domains"
        enable: true
        domain_patterns:
          patterns:
            - pattern: ".*\\.cdn\\.|.*\\.cloudfront\\.|.*\\.fastly\\."
              type: "regex"
              weight: 10
          relation: "or"
          case_sensitive: false

    matches:
      - name: "static_resource_match"
        enable: true
        conditions: ["static_resources"]
        actions:
          - type: "cache"
            duration: 86400  # 24小时缓存
            cache_scope: "global"
            custom_key: "static_{{.url_hash}}"
            max_use_count: 1000
          - type: "bypass_proxy"  # 静态资源直连
      - name: "cdn_optimization_match"
        enable: true
        conditions: ["cdn_domains"]
        actions:
          - type: "bypass_proxy"  # CDN域名直连
          - type: "cache"
            duration: 3600
            cache_scope: "domain"

  # 错误恢复和重试事件
  - name: "error_recovery_handler"
    enable: true
    trigger_type: "combined"
    process_stage: "post_body"
    priority: 20

    conditions:
      - name: "recoverable_errors"
        enable: true
        status_codes:
          codes: [502, 503, 504, 408, 429]
          relation: "or"
      - name: "timeout_errors"
        enable: true
        max_request_time: 30000  # 30秒超时

    matches:
      - name: "server_error_recovery"
        enable: true
        conditions: ["recoverable_errors"]
        actions:
          - type: "retry"
            retry_count: 2
          - type: "save_to_pool"
            pool_name: "error_proxies"
            quality_score: 30
      - name: "timeout_recovery"
        enable: true
        conditions: ["timeout_errors"]
        actions:
          - type: "retry_same"
            retry_count: 1
          - type: "banip"
            duration: 1800  # 30分钟临时封禁

  # 高级自定义触发器事件（展示JavaScript脚本功能）
  - name: "advanced_custom_trigger_handler"
    enable: true
    trigger_type: "custom"
    process_stage: "post_body"
    priority: 40

    conditions:
      - name: "advanced_custom_condition"
        enable: true
        custom_code: |
          // 高级自定义逻辑
          const url = opt.url || '';
          const status = opt.status || 0;
          const responseTime = opt.time_passed || 0;
          const userAgent = opt.headers['user-agent'] || '';
          const responseBody = opt.response_body || '';

          // 检测异常模式
          let anomalyScore = 0;

          // 1. 响应时间异常
          if (responseTime > 30000) anomalyScore += 20; // 超过30秒
          if (responseTime < 100) anomalyScore += 15;   // 小于100ms可能是缓存或错误

          // 2. 状态码异常
          if (status >= 500) anomalyScore += 25;
          if (status === 429) anomalyScore += 30; // 限流

          // 3. 响应内容异常
          if (responseBody.includes('error') || responseBody.includes('exception')) {
            anomalyScore += 15;
          }

          // 4. 特定域名的特殊处理
          if (url.includes('api.') && status !== 200) {
            anomalyScore += 20;
          }

          // 5. 用户代理异常
          if (userAgent.includes('bot') || userAgent.includes('crawler')) {
            anomalyScore += 10;
          }

          // 设置结果变量供后续使用
          opt.anomaly_score = anomalyScore;
          opt.risk_level = anomalyScore > 50 ? 'high' : (anomalyScore > 25 ? 'medium' : 'low');

          console.log('异常检测完成:', {
            url: url,
            anomalyScore: anomalyScore,
            riskLevel: opt.risk_level
          });

          return anomalyScore > 25; // 异常分数超过25触发

    matches:
      - name: "high_risk_anomaly"
        enable: true
        conditions: ["advanced_custom_condition"]
        actions:
          - type: "script"
            script: |
              // 根据风险等级执行不同动作
              const riskLevel = opt.risk_level || 'low';
              const anomalyScore = opt.anomaly_score || 0;

              if (riskLevel === 'high') {
                console.log('执行高风险处理流程');
                opt.ban_duration = 7200; // 2小时
                opt.log_level = 'error';
              } else if (riskLevel === 'medium') {
                console.log('执行中等风险处理流程');
                opt.ban_duration = 1800; // 30分钟
                opt.log_level = 'warn';
              } else {
                console.log('执行低风险处理流程');
                opt.ban_duration = 300; // 5分钟
                opt.log_level = 'info';
              }

              return true;
            language: "javascript"
          - type: "banip"
            duration: "{{.ban_duration}}"
          - type: "log"
            message: "异常检测触发: 风险等级={{.risk_level}}, 异常分数={{.anomaly_score}}, URL={{.url}}"
            level: "{{.log_level}}"

  # Google Tag Manager 检测事件
  - name: "googletagmanager_detection_handler"
    enable: true
    trigger_type: "body"
    process_stage: "post_body"
    priority: 5  # 高优先级，尽早处理

    conditions:
      - name: "gtm_content_check"
        enable: true
        body_patterns:
          patterns:
            - pattern: "www.googletagmanager.com"
              type: "string"
              weight: 10
          relation: "or"
          case_sensitive: false

    matches:
      - name: "gtm_detected_match"
        enable: true
        conditions: ["gtm_content_check"]
        actions:
          - type: "retry_same"
            retry_count: 3
          - type: "log"
            message: "🔍 检测到Google Tag Manager内容，使用相同代理重试3次 - URL: {{.url}} - 代理: {{.proxy}}"
            level: "info"

  # 复杂组合条件事件（展示高级条件组合）
  - name: "complex_combined_conditions_handler"
    enable: true
    trigger_type: "combined"
    process_stage: "post_body"
    priority: 35

    conditions:
      - name: "api_endpoint_errors"
        enable: true
        url_patterns:
          patterns:
            - pattern: "/api/v[0-9]+/"
              type: "regex"
              weight: 10
            - pattern: "/graphql"
              type: "string"
              weight: 8
          relation: "or"
        condition_relation: "AND"
        status_codes:
          codes: [400, 401, 403, 404, 422, 500, 502, 503]
          relation: "or"
      - name: "high_frequency_errors"
        enable: true
        max_request_time: 1000  # 快速失败
        condition_relation: "AND"
        response_header_patterns:
          headers:
            "X-RateLimit-Remaining":
              patterns:
                - pattern: "^[0-5]$"  # 剩余请求数很少
                  type: "regex"
                  weight: 15
              relation: "or"
      - name: "suspicious_client_behavior"
        enable: true
        request_header_patterns:
          headers:
            "User-Agent":
              patterns:
                - pattern: "^$|null|undefined"  # 空或异常User-Agent
                  type: "regex"
                  weight: 10
              relation: "or"
            "Referer":
              patterns:
                - pattern: "^$"  # 无Referer
                  type: "regex"
                  weight: 5
              relation: "or"

    matches:
      - name: "critical_api_abuse"
        enable: true
        conditions: ["api_endpoint_errors", "high_frequency_errors"]
        logic: "AND"
        actions:
          - type: "banipdomain"
            duration: 14400  # 4小时域名级封禁
            scope: "domain"
          - type: "log"
            message: "🚨 API滥用检测: 域名级封禁已执行 - {{.domain}} - {{.client_ip}}"
            level: "error"
      - name: "suspicious_behavior_monitoring"
        enable: true
        conditions: ["suspicious_client_behavior"]
        actions:
          - type: "log"
            message: "⚠️ 可疑客户端行为监控: {{.client_ip}} - UA: {{.user_agent}}"
            level: "warn"
          - type: "cache"
            duration: 300
            cache_scope: "global"
            custom_key: "suspicious_{{.client_ip}}"
            max_use_count: 5  # 限制访问频率

# ================================
# 高级配置
# ================================
# 定义错误恢复、追踪、性能和调试相关的高级配置
advanced:
  # 错误恢复配置
  error_recovery:
    max_retry_attempts: 3        # 最大重试次数: 0-10
    initial_retry_delay: "1s"    # 初始重试延迟
    max_retry_delay: "30s"       # 最大重试延迟
    retry_multiplier: 2.0        # 重试延迟倍数: >=1.0
    failure_threshold: 5         # 失败阈值: >=1
    success_threshold: 3         # 成功阈值: >=1
    circuit_timeout: "60s"       # 熔断器超时时间
    circuit_reset_timeout: "300s" # 熔断器重置超时时间

  # 追踪配置
  tracing:
    enabled: true                # 是否启用追踪
    hex_generator_length: 16     # 十六进制生成器长度: 8-64
    sequence_modulus: 10000      # 序列模数: >=1000

  # 性能配置
  performance:
    worker_pool_size: 10         # 工作池大小: >=1
    queue_size: 1000            # 队列大小: >=1
    batch_size: 100             # 批处理大小: >=1
    flush_interval: "5s"        # 刷新间隔

  # 调试配置
  debug:
    enabled: false              # 是否启用调试模式
    verbose_logging: false      # 详细日志记录
    dump_requests: false        # 转储请求
    dump_responses: false       # 转储响应
    profile_enabled: false      # 性能分析启用
    profile_port: 8081         # 性能分析端口: 1-65535

# ================================
# 路径配置
# ================================
# 定义各种文件和目录路径
paths:
  config_dir: "./config"          # 配置文件目录
  log_dir: "./logs"              # 日志文件目录
  cache_dir: "./cache"           # 缓存文件目录
  temp_dir: "./temp"             # 临时文件目录
  data_dir: "./data"             # 数据文件目录
  backup_dir: "./backup"         # 备份文件目录
  plugin_dir: "./plugins"        # 插件目录

# ================================
# 系统配置
# ================================
# 定义系统级别的配置选项
system:
  os_detection: true             # 操作系统检测
  arch_detection: true           # 架构检测

  # 信号处理配置
  signal_handling:
    graceful_shutdown: true      # 优雅关闭
    shutdown_timeout: "30s"      # 关闭超时时间
    signals: ["SIGTERM", "SIGINT", "SIGQUIT"] # 处理的信号列表

  # 资源限制配置
  limits:
    max_memory: "1GB"           # 最大内存使用
    max_cpu_percent: 80         # 最大CPU使用百分比: 1-100
    max_file_descriptors: 10000 # 最大文件描述符数: >=1
    max_goroutines: 10000       # 最大协程数: >=1

# ================================
# 协议配置
# ================================
# 定义各种网络协议的配置
protocols:
  # HTTP协议配置
  http:
    enabled: true
    version: "1.1"              # HTTP版本: 1.0, 1.1, 2.0
    keep_alive: true            # 保持连接
    compression: true           # 启用压缩 (注意: HTTPConfig中compression是bool类型)

  # HTTPS协议配置
  https:
    enabled: true
    version: "1.1"              # HTTPS版本: 1.0, 1.1, 2.0
    keep_alive: true            # 保持连接
    compression: true           # 启用压缩
    verify_ssl: true            # SSL验证

  # SOCKS4协议配置
  socks4:
    enabled: false

  # SOCKS5协议配置
  socks5:
    enabled: false
    auth_required: false

  # DNS协议配置
  dns:
    udp: true                   # 启用UDP DNS
    tcp: false                  # 启用TCP DNS
    tls: false                  # 启用DNS over TLS
    https: false                # 启用DNS over HTTPS
    doh: false                  # 启用DoH

# ================================
# 插件配置
# ================================
# 定义插件系统的配置
plugins:
  enabled: false                # 是否启用插件系统
  auto_load: true              # 自动加载插件
  plugin_paths: ["./plugins"]   # 插件搜索路径

  # 插件配置映射
  configs:
    example_plugin:
      enabled: false
      config_file: "example.yaml"
      parameters:
        param1: "value1"
        param2: 42

# ================================
# 开发配置
# ================================
# 定义开发和测试相关的配置
development:
  mode: "production"            # 模式: development, testing, production
  hot_reload: false            # 热重载

  # 测试配置
  testing:
    enabled: false              # 是否启用测试模式
    mock_responses: false       # 模拟响应
    test_data_dir: "./testdata" # 测试数据目录

  # 性能分析配置
  profiling:
    enabled: false              # 是否启用性能分析
    cpu_profile: false          # CPU性能分析
    memory_profile: false       # 内存性能分析
    block_profile: false        # 阻塞性能分析
    mutex_profile: false        # 互斥锁性能分析
