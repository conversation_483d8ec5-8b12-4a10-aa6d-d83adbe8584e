# FlexProxy Enabled 字段测试配置
# 此配置文件演示如何使用新增的 Enabled 字段来控制各个功能模块

# 全局配置
global:
  enable: true
  proxy_file: "./proxies.txt"
  dns_lookup_mode: "system"
  ip_rotation_mode: "random"
  min_proxy_pool_size: 1
  max_proxy_fetch_attempts: 3

# 服务器配置
server:
  host: "127.0.0.1"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"

# 代理配置 - 演示代理功能的启用/禁用
proxy:
  enabled: true  # 新增字段：控制代理功能
  strategy: "random"
  max_retries: 3
  retry_interval: "1s"
  pool_size: 10

# 缓存配置 - 演示缓存功能的启用/禁用
cache:
  enabled: true  # 新增字段：控制缓存功能
  type: "memory"
  ttl: "1h"
  size: 1000
  cleanup_interval: "10m"

# 日志配置 - 演示日志功能的启用/禁用
logging:
  enabled: true  # 新增字段：控制日志功能
  level: "info"
  format: "text"
  file: "./logs/flexproxy.log"
  max_size: 100
  max_age: 7
  max_backups: 3

# 监控配置
monitoring:
  enabled: true
  port: 9090
  path: "/metrics"

# 安全配置 - 演示安全功能的启用/禁用
security:
  enabled: true  # 新增字段：控制安全功能
  auth:
    type: "none"
    token_expiry: "24h"
  encryption:
    algorithm: "aes256"
    key_length: 32
  tls:
    enabled: false

# 限流配置
rate_limiting:
  enabled: false
  algorithm: "token_bucket"
  rate: 100
  burst: 200

# DNS服务配置
dns_service:
  enabled: true
  cache_ttl: "5m"
  timeout: "3s"
  retries: 2

# 高级配置 - 演示高级功能的启用/禁用
advanced:
  enabled: false  # 新增字段：控制高级功能（默认禁用）
  error_recovery:
    max_retry_attempts: 3
    initial_retry_delay: "1s"
    max_retry_delay: "30s"
    retry_multiplier: 2.0
  tracing:
    enabled: false
    hex_generator_length: 16
  performance:
    worker_pool_size: 10
    queue_size: 1000
    batch_size: 100
  debug:
    enabled: false
    verbose_logging: false
    dump_requests: false
    dump_responses: false

# 开发配置 - 演示开发功能的启用/禁用
development:
  enabled: false  # 新增字段：控制开发功能（默认禁用）
  mode: "production"
  hot_reload: false
  testing:
    enabled: false
    mock_responses: false
    test_data_dir: "./testdata"
  profiling:
    enabled: false
    cpu_profile: false
    memory_profile: false
    block_profile: false
    mutex_profile: false

# 路径配置
paths:
  base_dir: "."
  config_dir: "./config"
  logs_dir: "./logs"
  data_dir: "./data"

# 系统配置
system:
  os_detection: true
  arch_detection: true
  signal_handling:
    graceful_shutdown: true
    shutdown_timeout: "30s"

# 协议配置
protocols:
  http:
    enabled: true
    version: "1.1"
    keep_alive: true
  https:
    enabled: true
    version: "1.1"
    verify_ssl: true
  socks4:
    enabled: false
  socks5:
    enabled: false

# 插件配置
plugins:
  enabled: false
  directory: "./plugins"
  auto_load: false

# 动作配置
actions:
  default_handler:
    sequence:
      - type: "log"
        message: "默认处理器"
        level: "info"

# 事件配置
events:
  - name: "test_event"
    enable: true
    trigger_type: "status"
    process_stage: "post_header"
    priority: 10
    conditions:
      - name: "status_check"
        enable: true
        status_codes:
          codes: [500, 502, 503]
          relation: "or"
    matches:
      - name: "error_match"
        enable: true
        conditions: ["status_check"]
        actions:
          - type: "log"
            message: "检测到错误状态码"
            level: "warn"
