// Package examples 展示如何使用 Enabled 配置字段
package main

import (
	"fmt"
	"log"
	
	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/common/logger"
)

// 示例：如何在应用启动时使用 Enabled 配置检查
func main() {
	// 1. 加载配置文件
	config, err := common.LoadConfigFromYAML("config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 2. 创建配置检查器
	checker := common.NewConfigChecker(config)
	
	// 3. 记录所有启用的功能
	checker.LogEnabledFeatures()
	
	// 4. 验证配置
	if err := checker.ValidateEnabledFeatures(); err != nil {
		log.Printf("配置验证警告: %v", err)
	}

	// 5. 创建配置应用器
	applier := common.NewConfigApplier(config)
	
	// 6. 根据配置创建服务
	demonstrateServiceCreation(applier)
	
	// 7. 展示功能摘要
	demonstrateFeatureSummary(applier)
}

// demonstrateServiceCreation 展示如何根据配置创建服务
func demonstrateServiceCreation(applier *common.ConfigApplier) {
	fmt.Println("\n=== 服务创建演示 ===")
	
	// 创建缓存服务
	cacheService := applier.CreateCacheService()
	fmt.Printf("缓存服务类型: %T\n", cacheService)
	
	// 创建日志服务
	logService := applier.CreateLogService()
	fmt.Printf("日志服务类型: %T\n", logService)
	
	// 创建安全服务
	securityService := applier.CreateSecurityService()
	fmt.Printf("安全服务类型: %T\n", securityService)
	
	// 创建高级服务
	advancedServices := applier.CreateAdvancedServices()
	fmt.Printf("高级服务数量: %d\n", len(advancedServices))
	for name, service := range advancedServices {
		if service != nil {
			fmt.Printf("  - %s: %T\n", name, service)
		} else {
			fmt.Printf("  - %s: 已禁用\n", name)
		}
	}
	
	// 创建开发服务
	devServices := applier.CreateDevelopmentServices()
	fmt.Printf("开发服务数量: %d\n", len(devServices))
	for name, service := range devServices {
		if service != nil {
			fmt.Printf("  - %s: %T\n", name, service)
		} else {
			fmt.Printf("  - %s: 已禁用\n", name)
		}
	}
}

// demonstrateFeatureSummary 展示功能摘要
func demonstrateFeatureSummary(applier *common.ConfigApplier) {
	fmt.Println("\n=== 功能状态摘要 ===")
	
	summary := applier.GetEnabledFeaturesSummary()
	for feature, enabled := range summary {
		status := "❌ 禁用"
		if enabled {
			status = "✅ 启用"
		}
		fmt.Printf("%s: %s\n", feature, status)
	}
}

// demonstrateConditionalLogic 展示条件逻辑的使用
func demonstrateConditionalLogic(config *common.Config) {
	fmt.Println("\n=== 条件逻辑演示 ===")
	
	checker := common.NewConfigChecker(config)
	
	// 根据缓存配置决定是否执行缓存相关操作
	if checker.IsCacheEnabled() {
		fmt.Println("执行缓存初始化...")
		// 实际的缓存初始化代码
	} else {
		fmt.Println("跳过缓存初始化")
	}
	
	// 根据安全配置决定是否启用安全功能
	if checker.IsSecurityEnabled() {
		fmt.Println("启用安全中间件...")
		// 实际的安全中间件初始化代码
	} else {
		fmt.Println("跳过安全中间件")
	}
	
	// 根据开发配置决定是否启用调试功能
	if checker.IsDevelopmentEnabled() {
		fmt.Println("启用开发模式功能...")
		// 实际的开发模式初始化代码
		
		// 检查具体的开发功能
		if config.Development != nil {
			if config.Development.HotReload {
				fmt.Println("  - 启用热重载")
			}
			if config.Development.Testing != nil && config.Development.Testing.Enabled {
				fmt.Println("  - 启用测试模式")
			}
			if config.Development.Profiling != nil && config.Development.Profiling.Enabled {
				fmt.Println("  - 启用性能分析")
			}
		}
	} else {
		fmt.Println("跳过开发模式功能")
	}
}

// demonstrateServiceUsage 展示如何在运行时检查服务状态
func demonstrateServiceUsage() {
	fmt.Println("\n=== 服务使用演示 ===")
	
	// 模拟在运行时检查服务是否可用
	logger := logger.GetLogger("example")
	
	// 示例：在使用缓存前检查是否启用
	checkAndUseCache := func() {
		// 这里应该从依赖注入容器获取缓存服务
		// cacheService := container.GetCacheService()
		
		// 检查缓存服务是否为空实现
		// if _, ok := cacheService.(*services.NullCacheService); ok {
		//     logger.Debug("缓存服务已禁用，跳过缓存操作")
		//     return
		// }
		
		logger.Info("执行缓存操作...")
	}
	
	checkAndUseCache()
	
	// 示例：在使用安全功能前检查是否启用
	checkAndUseSecurity := func() {
		// 这里应该从依赖注入容器获取安全服务
		// securityService := container.GetSecurityService()
		
		// 检查安全服务是否为空实现
		// if _, ok := securityService.(*services.NullSecurityService); ok {
		//     logger.Debug("安全服务已禁用，跳过安全检查")
		//     return
		// }
		
		logger.Info("执行安全检查...")
	}
	
	checkAndUseSecurity()
}
