# DNS服务器配置验证更新

## 概述

FlexProxy的DNS服务器配置验证已经更新，现在支持更灵活的服务器地址格式。

## 更新内容

### 之前的限制
- `server`字段只能使用纯IP地址格式（如`*******`）
- 验证标签：`validate:"required,ip"`
- 端口配置只能通过protocol字段的默认端口处理

### 现在的支持
- `server`字段支持两种格式：
  1. **纯IP地址**：`*******`、`2001:4860:4860::8888`
  2. **IP:端口格式**：`*******:53`、`[2001:4860:4860::8888]:53`
- 验证标签：`validate:"required,ip_or_ip_port"`
- 完整的IPv4和IPv6支持

## 技术实现

### 新增自定义验证器
```go
// validateIPOrIPPort 验证IP地址或IP:端口格式
func validateIPOrIPPort(fl validator.FieldLevel) bool {
    value := fl.Field().String()
    if value == "" {
        return false
    }
    
    // 首先尝试直接解析为IP地址（包括IPv6）
    if net.ParseIP(value) != nil {
        return true
    }
    
    // 如果不是纯IP地址，尝试解析为IP:端口格式
    host, port, err := net.SplitHostPort(value)
    if err != nil {
        return false
    }
    
    // 验证IP地址和端口号
    if net.ParseIP(host) == nil {
        return false
    }
    
    portNum, err := strconv.Atoi(port)
    if err != nil || portNum < 1 || portNum > 65535 {
        return false
    }
    
    return true
}
```

### 配置结构更新
```go
type CustomDNSServer struct {
    Server   string   `yaml:"server" validate:"required,ip_or_ip_port"`
    Protocol string   `yaml:"protocol" validate:"required,oneof=udp tcp doh dot"`
    Timeout  int      `yaml:"timeout,omitempty" validate:"min=1000,max=30000"`
    Priority int      `yaml:"priority,omitempty" validate:"min=0,max=100"`
    Tags     []string `yaml:"tags,omitempty"`
}
```

## 配置示例

### 支持的格式
```yaml
custom_dns_servers:
  # 纯IPv4地址
  - server: "*******"
    protocol: "udp"
    
  # IPv4:端口格式
  - server: "*******:53"
    protocol: "tcp"
    
  # 纯IPv6地址
  - server: "2001:4860:4860::8888"
    protocol: "udp"
    
  # IPv6:端口格式
  - server: "[2001:4860:4860::8888]:53"
    protocol: "tcp"
    
  # 自定义端口
  - server: "***********:5353"
    protocol: "udp"
```

### 不支持的格式
```yaml
# ❌ 域名（不支持）
- server: "dns.google.com"

# ❌ 无效IP
- server: "999.999.999.999"

# ❌ 无效端口
- server: "*******:99999"

# ❌ 空字符串
- server: ""
```

## 验证测试

所有格式都经过了完整的验证测试：
- ✅ 纯IPv4地址
- ✅ IPv4:端口格式
- ✅ 纯IPv6地址
- ✅ IPv6:端口格式
- ✅ 无效格式正确拒绝

## 向后兼容性

此更新完全向后兼容：
- 现有的纯IP地址配置继续有效
- 新的IP:端口格式提供更多灵活性
- 不会破坏现有配置文件

## 使用建议

1. **明确端口**：建议使用IP:端口格式明确指定端口号
2. **IPv6支持**：充分利用IPv6 DNS服务器
3. **企业环境**：可以配置自定义端口的内网DNS服务器
4. **协议匹配**：确保端口与协议匹配（如DoT使用853端口）
