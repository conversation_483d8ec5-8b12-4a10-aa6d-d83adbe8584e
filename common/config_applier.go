package common

import (
	"fmt"
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// ConfigApplier 配置应用器，根据配置创建相应的服务实例
type ConfigApplier struct {
	config  *Config
	checker *ConfigChecker
	logger  logger.Logger
}

// NewConfigApplier 创建配置应用器
func NewConfigApplier(config *Config) *ConfigApplier {
	return &ConfigApplier{
		config:  config,
		checker: NewConfigChecker(config),
		logger:  logger.GetLogger("config-applier"),
	}
}

// ServiceCreationConfig 服务创建配置
type ServiceCreationConfig struct {
	CacheEnabled       bool
	LoggingEnabled     bool
	SecurityEnabled    bool
	AdvancedEnabled    bool
	DevelopmentEnabled bool
}

// GetServiceCreationConfig 获取服务创建配置
func (ca *ConfigApplier) GetServiceCreationConfig() *ServiceCreationConfig {
	return &ServiceCreationConfig{
		CacheEnabled:       ca.checker.IsCacheEnabled(),
		LoggingEnabled:     ca.checker.IsLoggingEnabled(),
		SecurityEnabled:    ca.checker.IsSecurityEnabled(),
		AdvancedEnabled:    ca.checker.IsAdvancedEnabled(),
		DevelopmentEnabled: ca.checker.IsDevelopmentEnabled(),
	}
}

// GetSecurityConfig 获取安全配置
func (ca *ConfigApplier) GetSecurityConfig() *SecurityConfig {
	if ca.config.Security != nil {
		return ca.config.Security
	}

	// 返回默认安全配置
	return &SecurityConfig{
		Enabled: true,
		Auth: &AuthConfig{
			Type:        "none",
			TokenExpiry: "24h",
		},
		Encryption: &EncryptionConfig{
			Algorithm: "aes256",
			KeyLength: 32,
		},
	}
}

// CreateAdvancedServices 根据配置创建高级服务
func (ca *ConfigApplier) CreateAdvancedServices() map[string]interface{} {
	services := make(map[string]interface{})
	
	if !ca.checker.IsAdvancedEnabled() {
		ca.logger.Info("高级功能已禁用，创建空实现")
		services["advanced_config"] = services.NewNullAdvancedConfigService(ca.logger.GetRawLogger())
		services["tracing"] = nil // 可以添加空追踪服务
		services["performance"] = nil // 可以添加空性能服务
		return services
	}
	
	ca.logger.Info("创建高级服务")
	advancedConfig := ca.config.Advanced
	if advancedConfig == nil {
		advancedConfig = &AdvancedConfig{
			Enabled: true,
			ErrorRecovery: &ErrorRecoveryConfig{
				MaxRetryAttempts:    3,
				InitialRetryDelay:   "1s",
				MaxRetryDelay:       "30s",
				RetryMultiplier:     2.0,
				FailureThreshold:    5,
				SuccessThreshold:    3,
				CircuitTimeout:      "60s",
				CircuitResetTimeout: "300s",
			},
		}
	}
	
	services["advanced_config"] = services.NewAdvancedConfigService(advancedConfig, ca.logger.GetRawLogger())
	
	// 根据高级配置创建其他服务
	if advancedConfig.Tracing != nil && advancedConfig.Tracing.Enabled {
		services["tracing"] = services.NewTracingService(advancedConfig.Tracing, ca.logger.GetRawLogger())
	}
	
	if advancedConfig.Performance != nil {
		services["performance"] = services.NewPerformanceService(advancedConfig.Performance, ca.logger.GetRawLogger())
	}
	
	if advancedConfig.Debug != nil && advancedConfig.Debug.Enabled {
		services["debug"] = services.NewDebugService(advancedConfig.Debug, ca.logger.GetRawLogger())
	}
	
	return services
}

// CreateDevelopmentServices 根据配置创建开发服务
func (ca *ConfigApplier) CreateDevelopmentServices() map[string]interface{} {
	services := make(map[string]interface{})
	
	if !ca.checker.IsDevelopmentEnabled() {
		ca.logger.Info("开发功能已禁用，创建空实现")
		services["debug"] = services.NewNullDebugService(ca.logger.GetRawLogger())
		services["profiling"] = services.NewNullProfilingService(ca.logger.GetRawLogger())
		return services
	}
	
	ca.logger.Info("创建开发服务")
	devConfig := ca.config.Development
	
	// 创建调试服务
	if devConfig.Testing != nil && devConfig.Testing.Enabled {
		// 可以创建测试相关的服务
		ca.logger.Info("测试模式已启用")
	}
	
	// 创建性能分析服务
	if devConfig.Profiling != nil && devConfig.Profiling.Enabled {
		services["profiling"] = services.NewProfilingService(devConfig.Profiling, ca.logger.GetRawLogger())
	} else {
		services["profiling"] = services.NewNullProfilingService(ca.logger.GetRawLogger())
	}
	
	return services
}

// ApplyProxyConfiguration 应用代理配置
func (ca *ConfigApplier) ApplyProxyConfiguration() error {
	if !ca.checker.IsProxyEnabled() {
		ca.logger.Warn("代理功能已禁用 - 这可能影响核心功能")
		return fmt.Errorf("代理功能已禁用")
	}
	
	ca.logger.Info("应用代理配置")
	// 这里可以添加代理配置的具体应用逻辑
	return nil
}

// GetEnabledFeaturesSummary 获取启用功能的摘要
func (ca *ConfigApplier) GetEnabledFeaturesSummary() map[string]bool {
	return map[string]bool{
		"cache":       ca.checker.IsCacheEnabled(),
		"logging":     ca.checker.IsLoggingEnabled(),
		"security":    ca.checker.IsSecurityEnabled(),
		"proxy":       ca.checker.IsProxyEnabled(),
		"advanced":    ca.checker.IsAdvancedEnabled(),
		"development": ca.checker.IsDevelopmentEnabled(),
		"monitoring":  ca.checker.IsMonitoringEnabled(),
	}
}
